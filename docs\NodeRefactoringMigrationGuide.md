# 节点重构迁移指南

## 概述

本次重构将 `Node.NextNode` 属性替换为基于接口的转换器系统，以提供更灵活和可扩展的节点转换逻辑。

## 主要变更

### 1. 移除的属性
- `Node.NextNode` - 单一后继节点属性

### 2. 新增的接口和类
- `INodeTransition` - 节点转换器接口
- `SimpleNodeTransition` - 简单转换器（替代 NextNode）
- `ParallelSplitTransition` - 并行分支转换器
- `ConditionalTransition` - 条件转换器
- `EmptyTransition` - 空转换器
- `NodeTransitionFactory` - 转换器工厂

### 3. 更新的方法
- `Node.TryNextNode()` - 现在委托给转换器
- `Node.GetNextNodes()` - 新增方法，获取所有可能的下一节点
- `Node.Validate()` - 现在委托给转换器

## 迁移步骤

### 步骤 1: 更新节点类

#### 旧代码
```csharp
public class StartNode : Node
{
    public StartNode(Node nextNode, WorkProcess wp) : base(wp)
    {
        NextNode = nextNode;
    }
}
```

#### 新代码
```csharp
public class StartNode : Node
{
    public StartNode(Node nextNode, WorkProcess wp) : base(wp)
    {
        Transition = new SimpleNodeTransition(nextNode);
    }
}
```

### 步骤 2: 更新节点创建逻辑

#### 旧代码
```csharp
var startNode = new StartNode(nextNode, workProcess);
startNode.NextNode = nextNode;
```

#### 新代码
```csharp
var startNode = new StartNode(nextNode, workProcess);
// 或者
startNode.Transition = NodeTransitionFactory.CreateSimple(nextNode);
```

### 步骤 3: 更新验证逻辑

#### 旧代码
```csharp
public override void Validate()
{
    if (NextNode == null)
    {
        throw new WorkflowDefinedException("必须指定下一个节点");
    }
}
```

#### 新代码
```csharp
public override void Validate()
{
    base.Validate(); // 这会调用转换器的验证逻辑
}
```

## 转换器类型说明

### SimpleNodeTransition
- **用途**: 替代原来的 `NextNode` 属性
- **适用场景**: 单一后继节点的简单流程
- **示例**: 开始节点、普通工作任务节点

### ParallelSplitTransition
- **用途**: 处理并行分支的多个后继节点
- **适用场景**: 并行分支节点
- **特点**: `TryNextNode` 返回 false，需要特殊处理

### ConditionalTransition
- **用途**: 基于条件决定下一个节点
- **适用场景**: 条件分支节点
- **特点**: 支持多个条件和优先级

### EmptyTransition
- **用途**: 不需要转换的节点
- **适用场景**: 结束节点
- **特点**: 始终返回 false

## 向后兼容性

为了保持向后兼容性，建议：

1. **分阶段迁移**: 先迁移核心节点类型，再迁移其他节点
2. **保持接口一致**: 新的转换器系统保持与原有 `TryNextNode` 方法的兼容性
3. **逐步替换**: 逐步将 `NextNode` 属性替换为相应的转换器

## 测试建议

1. **单元测试**: 为每个转换器类型编写单元测试
2. **集成测试**: 测试工作流的完整流程
3. **性能测试**: 验证新系统的性能表现
4. **回归测试**: 确保现有功能不受影响

## 注意事项

1. **转换器必须设置**: 所有节点都必须设置 `Transition` 属性
2. **验证逻辑**: 转换器的验证逻辑会替代原有的验证逻辑
3. **并行分支**: 并行分支节点需要特殊处理，不能通过 `TryNextNode` 获取
4. **条件分支**: 条件分支需要实现 `TransitionCondition.Evaluate` 方法

## 示例代码

### 创建条件分支节点
```csharp
var conditions = new List<TransitionCondition>
{
    new TransitionCondition("amount > 1000", highPriorityNode, 2),
    new TransitionCondition("amount > 500", mediumPriorityNode, 1),
    new TransitionCondition("amount > 0", lowPriorityNode, 0)
};

var conditionalNode = new WorkTask("审批", workProcess);
conditionalNode.Transition = NodeTransitionFactory.CreateConditional(conditions, defaultNode);
```

### 创建并行分支节点
```csharp
var parallelNode = new ParallelSplitNode(workProcess);
parallelNode.AddNextNode(task1);
parallelNode.AddNextNode(task2);
parallelNode.AddNextNode(task3);
```

## 总结

新的转换器系统提供了：
- **更好的扩展性**: 可以轻松添加新的转换逻辑
- **更清晰的职责分离**: 节点负责业务逻辑，转换器负责流程控制
- **更灵活的流程控制**: 支持复杂的条件分支和并行分支
- **更好的可测试性**: 转换逻辑可以独立测试

通过这次重构，工作流引擎将更加灵活和强大。
