﻿using Coder.ScriptWorkflow.Decisions;
using Coder.ScriptWorkflow.Decisions.BooleanDecisions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Coder.ScriptWorkflow.Mapping;

internal class ConditionDecisionMapping : IEntityTypeConfiguration<ConditionDecision>
{
    public void Configure(EntityTypeBuilder<ConditionDecision> builder)
    {
        builder.HasBaseType<Decision>();
        builder.Property(_ => _.Script).HasColumnName("ConditionDecision_Script").HasColumnType("text").HasComment("执行脚本"); ;
        builder.HasMany(decision => decision.Settings);
    
    }
}