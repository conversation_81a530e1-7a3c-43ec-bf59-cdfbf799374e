using Coder.ScriptWorkflow.Nodes;
using Coder.ScriptWorkflow.Stores;
using Coder.ScriptWorkflow.ViewModels.Defined;

namespace Coder.ScriptWorkflow.DtoTranslator.Define;

/// <summary>
/// 汇聚节点转换器
/// </summary>
internal class ParallelJoinNodeTranslator : SingleNodeTranslator<ParallelJoinNodeSubmit, ParallelJoinNode>
{
    /// <summary>
    /// 将DTO数据填充到实体
    /// </summary>
    /// <param name="node">汇聚节点实体</param>
    /// <param name="submit">汇聚节点DTO</param>
    /// <param name="context">工作流提交上下文</param>
    /// <param name="nodeStore">节点存储</param>
    protected override void FillToEntity(ParallelJoinNode node, ParallelJoinNodeSubmit submit, WorkflowSubmitContext context, INodeStore nodeStore)
    {
        // 设置等待的WorkTask列表
        node.WaitForWorkTasks.Clear();
        node.WaitForWorkTasks.AddRange(submit.WaitForWorkTasks);

        // 转换汇聚条件
        node.JoinCondition = submit.JoinCondition switch
        {
            JoinConditionSubmit.All => JoinCondition.All,
            JoinConditionSubmit.Any => JoinCondition.Any,
            JoinConditionSubmit.Custom => JoinCondition.Custom,
            _ => JoinCondition.All
        };

        // 设置自定义汇聚脚本
        node.CustomJoinScript = submit.CustomJoinScript;
    }



    /// <summary>
    /// 将实体数据填充到DTO
    /// </summary>
    /// <param name="src">汇聚节点实体</param>
    /// <param name="target">汇聚节点DTO</param>
    protected override void FillToViewModel(ParallelJoinNode src, ParallelJoinNodeSubmit target)
    {
        target.WaitForWorkTasks = new System.Collections.Generic.List<string>(src.WaitForWorkTasks);

        // 转换汇聚条件
        target.JoinCondition = src.JoinCondition switch
        {
            JoinCondition.All => JoinConditionSubmit.All,
            JoinCondition.Any => JoinConditionSubmit.Any,
            JoinCondition.Custom => JoinConditionSubmit.Custom,
            _ => JoinConditionSubmit.All
        };

        target.CustomJoinScript = src.CustomJoinScript;
    }

    /// <summary>
    /// 构建节点关系
    /// </summary>
    /// <param name="context">工作流提交上下文</param>
    /// <param name="node">节点</param>
    /// <param name="nodeSubmit">节点DTO</param>
    protected override void BuildEntityRelation(WorkflowSubmitContext context, ParallelJoinNode node, ParallelJoinNodeSubmit nodeSubmit)
    {
        // 汇聚节点使用标准的NextNode关系
        if (context.WorkProcess.Enable && string.IsNullOrEmpty(nodeSubmit.NextNodeName))
            throw new WorkflowDefinedException(nodeSubmit.Name + "需要设置'下一个节点'");

        if (nodeSubmit.NextNodeName != null)
            node.NextNode = context.GetNode(nodeSubmit.NextNodeName);
    }
}
