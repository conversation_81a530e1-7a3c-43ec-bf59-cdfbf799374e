using System;
using System.Collections.Generic;
using System.Linq;

namespace Coder.ScriptWorkflow.Nodes.Transitions;

/// <summary>
///     条件转换器 - 基于条件决定下一个节点
/// </summary>
public class ConditionalTransition : INodeTransition
{
    private readonly List<TransitionCondition> _conditions = new();

    /// <summary>
    ///     构造函数
    /// </summary>
    public ConditionalTransition()
    {
    }

    /// <summary>
    ///     构造函数
    /// </summary>
    /// <param name="conditions">转换条件集合</param>
    public ConditionalTransition(IEnumerable<TransitionCondition> conditions)
    {
        if (conditions != null)
        {
            foreach (var condition in conditions)
            {
                AddCondition(condition);
            }
        }
    }

    /// <summary>
    ///     转换条件集合
    /// </summary>
    public IReadOnlyList<TransitionCondition> Conditions => _conditions.AsReadOnly();

    /// <summary>
    ///     默认下一个节点（当所有条件都不满足时使用）
    /// </summary>
    public Node DefaultNextNode { get; set; }

    /// <summary>
    ///     添加转换条件
    /// </summary>
    /// <param name="condition">转换条件</param>
    public void AddCondition(TransitionCondition condition)
    {
        if (condition == null)
            throw new ArgumentNullException(nameof(condition));
            
        if (!_conditions.Contains(condition))
        {
            _conditions.Add(condition);
        }
    }

    /// <summary>
    ///     移除转换条件
    /// </summary>
    /// <param name="condition">要移除的条件</param>
    public void RemoveCondition(TransitionCondition condition)
    {
        if (condition != null)
        {
            _conditions.Remove(condition);
        }
    }

    /// <inheritdoc />
    public bool TryGetNextNode(Node currentNode, IWorkflowContext workflowContext, out Node nextNode)
    {
        // 按优先级顺序评估条件
        foreach (var condition in _conditions.OrderByDescending(c => c.Priority))
        {
            if (condition.Evaluate(workflowContext))
            {
                nextNode = condition.NextNode;
                return true;
            }
        }

        // 如果没有条件满足，使用默认节点
        if (DefaultNextNode != null)
        {
            nextNode = DefaultNextNode;
            return true;
        }

        nextNode = null;
        return false;
    }

    /// <inheritdoc />
    public IEnumerable<Node> GetNextNodes(Node currentNode, IWorkflowContext workflowContext)
    {
        var nodes = new HashSet<Node>();
        
        // 添加所有条件节点
        foreach (var condition in _conditions)
        {
            if (condition.NextNode != null)
            {
                nodes.Add(condition.NextNode);
            }
        }
        
        // 添加默认节点
        if (DefaultNextNode != null)
        {
            nodes.Add(DefaultNextNode);
        }
        
        return nodes;
    }

    /// <inheritdoc />
    public void Validate(Node currentNode)
    {
        if (!_conditions.Any())
        {
            throw new WorkflowDefinedException($"条件转换器节点 '{currentNode.Name}' 必须至少有一个转换条件", currentNode.Name);
        }

        // 验证所有条件都有有效的下一个节点
        var invalidConditions = _conditions.Where(c => c.NextNode == null).ToList();
        if (invalidConditions.Any())
        {
            throw new WorkflowDefinedException($"条件转换器节点 '{currentNode.Name}' 的所有条件都必须指定下一个节点", currentNode.Name);
        }

        // 验证条件优先级不重复
        var duplicatePriorities = _conditions.GroupBy(c => c.Priority)
            .Where(g => g.Count() > 1)
            .Select(g => g.Key)
            .ToList();
            
        if (duplicatePriorities.Any())
        {
            var priorities = string.Join(", ", duplicatePriorities);
            throw new WorkflowDefinedException($"条件转换器节点 '{currentNode.Name}' 存在重复的优先级: {priorities}", currentNode.Name);
        }
    }
}

/// <summary>
///     转换条件
/// </summary>
public class TransitionCondition
{
    /// <summary>
    ///     构造函数
    /// </summary>
    public TransitionCondition()
    {
    }

    /// <summary>
    ///     构造函数
    /// </summary>
    /// <param name="conditionScript">条件脚本</param>
    /// <param name="nextNode">下一个节点</param>
    /// <param name="priority">优先级（数字越大优先级越高）</param>
    public TransitionCondition(string conditionScript, Node nextNode, int priority = 0)
    {
        ConditionScript = conditionScript ?? throw new ArgumentNullException(nameof(conditionScript));
        NextNode = nextNode ?? throw new ArgumentNullException(nameof(nextNode));
        Priority = priority;
    }

    /// <summary>
    ///     条件脚本
    /// </summary>
    public string ConditionScript { get; set; }

    /// <summary>
    ///     下一个节点
    /// </summary>
    public Node NextNode { get; set; }

    /// <summary>
    ///     优先级（数字越大优先级越高）
    /// </summary>
    public int Priority { get; set; }

    /// <summary>
    ///     评估条件
    /// </summary>
    /// <param name="workflowContext">工作流上下文</param>
    /// <returns>条件是否满足</returns>
    public virtual bool Evaluate(IWorkflowContext workflowContext)
    {
        // TODO: 实现脚本执行逻辑
        // 这里应该执行 ConditionScript 并返回结果
        // 暂时返回 false，需要根据实际的脚本执行引擎来实现
        return false;
    }
}
