﻿using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Threading.Tasks;
using Coder.ScriptWorkflow.Assigners;
using Coder.ScriptWorkflow.Decisions.BooleanDecisions;
using Coder.ScriptWorkflow.Interceptors;
using Coder.ScriptWorkflow.Nodes;
using Coder.ScriptWorkflow.Permissions;
using Coder.ScriptWorkflow.Scripts;
using Coder.ScriptWorkflow.Stores;
using Coder.ScriptWorkflow.ViewModels;
using Coder.ScriptWorkflow.ViewModels.Defined;
using Coder.ScriptWorkflow.WorkTaskCommands;
using Microsoft.Extensions.Logging;

namespace Coder.ScriptWorkflow;

/// <summary>
/// </summary>
public class WorkflowDefinedManager
{
    private readonly IGlobalScriptStore _globalScriptStore;
    private readonly InterceptorManager _interceptorManager;
    private readonly ILogger<WorkflowDefinedManager> _logger;
    private readonly INodeStore _nodeStore;
    private readonly IProcessInstanceStore _processInstanceStore;
    private readonly ITransactionFactory _transactionFactory;
    private readonly IWorkActivityStore _workActivityStore;
    private readonly IWorkProcessPermissionStore _workProcessPermissionStore;
    private readonly IWorkProcessStore _workProcessStore;

    private readonly IWorkTaskStore _workTaskStore;

    /// <summary>
    /// </summary>
    /// <param name="workProcessStore"></param>
    /// <param name="nodeStore"></param>
    /// <param name="workTaskStore"></param>
    /// <param name="processInstanceStore"></param>
    /// <param name="transactionFactory"></param>
    /// <param name="workActivityStore"></param>
    /// <param name="globalScriptStore"></param>
    /// <param name="workProcessPermissionStore"></param>
    /// <param name="interceptorManager"></param>
    /// <param name="logger"></param>
    public WorkflowDefinedManager(IWorkProcessStore workProcessStore, INodeStore nodeStore,
        IWorkTaskStore workTaskStore, IProcessInstanceStore processInstanceStore,
        ITransactionFactory transactionFactory,
        IWorkActivityStore workActivityStore, IGlobalScriptStore globalScriptStore,
        IWorkProcessPermissionStore workProcessPermissionStore, InterceptorManager interceptorManager,
        ILogger<WorkflowDefinedManager> logger)
    {
        _workProcessStore = workProcessStore;
        _nodeStore = nodeStore;
        _workTaskStore = workTaskStore;
        _processInstanceStore = processInstanceStore;
        _transactionFactory = transactionFactory;
        _workActivityStore = workActivityStore;
        _globalScriptStore = globalScriptStore;
        _workProcessPermissionStore = workProcessPermissionStore;
        _interceptorManager = interceptorManager;
        _logger = logger;
    }

    /// <summary>
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [return: MaybeNull]
    public WorkProcess Get(int id)
    {
        if (id < 0)
            throw new ArgumentOutOfRangeException(nameof(id), "id 必须大于0");
        return _workProcessStore.Get(id);
    }

    /// <summary>
    /// </summary>
    /// <param name="name">workProcess's name</param>
    /// <param name="version"></param>
    /// <param name="notIncludeId"></param>
    /// <exception cref="ArgumentNullException">当name为空</exception>
    /// <returns></returns>
    public bool IsExistWorkProcessName(string name, int? version, int? notIncludeId)
    {
        if (name == null) throw new ArgumentNullException(nameof(name));
        var exist = _workProcessStore.Exist(name, version, notIncludeId);
        return exist;
    }

    /// <summary>
    /// </summary>
    /// <param name="workProcessId"></param>
    /// <param name="name"></param>
    /// <param name="notIncludeId"></param>
    /// <returns></returns>
    public bool IsExistWorkTaskName(int workProcessId, string name, int? notIncludeId)
    {
        if (name == null) throw new ArgumentNullException(nameof(name));
        if (workProcessId < 0)
            throw new ArgumentOutOfRangeException(nameof(workProcessId), "workProcessId不能少于或等于0.");
        var exist = _workTaskStore.Exist(workProcessId, name, notIncludeId);
        return exist;
    }

    /// <summary>
    ///     列出同一个名字，所有版本的工作流定义。
    /// </summary>
    /// <param name="name"></param>
    /// <returns></returns>
    public IEnumerable<WorkProcess> Get(string name)
    {
        if (name == null) throw new ArgumentNullException(nameof(name));
        return _workProcessStore.ListByName(name);
    }

    /// <summary>
    /// </summary>
    /// <param name="submit"></param>
    /// <returns></returns>
    /// <exception cref="WorkflowDefinedException"></exception>
    public SwfResult<SaveWorkProcessResult> Save(WorkProcessSubmit submit)
    {
        var result = new SaveWorkProcessResult();


        //if (submit.Enable && !submit.Validate(out var message)) return result.ToError(message, -1);


        if (submit.Enable)
            if (_workProcessStore.ExistNumberPrefix(submit.Prefix, submit.Name, submit.Id))
                throw new WorkflowDefinedException(submit.Prefix + "已经有其他工作流使用，请更换。");

        if (!submit.OverWrite && IsExistWorkProcessName(submit.Name, submit.Version, submit.Id))
        {
            result.Message = "已经有相同名字的工作流。如果确认要保存，请设置覆盖提交选项。";
            return result.ToError("已经有相同名字的工作流。如果确认要保存，请设置覆盖提交选项。", -1);
        }

        var transManager = _transactionFactory.BeginTransactions();

        try
        {
            WorkProcess workProcess = null;
            if (submit.Id == 0) //只要wp提交的id为0。都应该是新的
                submit.OverWrite = false;

            if (submit.Id == 0)
            {
                workProcess = new WorkProcess(submit.Name);
            }
            else
            {
                if (submit.OverWrite)
                {
                    workProcess = _workProcessStore.Get(submit.Id);
                }
                else
                {
                    workProcess = new WorkProcess(submit.Name);
                    foreach (var node in submit.Nodes) node.Id = 0;
                }
            }

            var workProcessSrcName = workProcess.Name;

            if (!submit.OverWrite && workProcess.Enable)
            {
                var duplicateWp = _workProcessStore.GetByNameVersion(submit.Name, submit.Version);
                if (duplicateWp != null)
                    throw new WorkflowDefinedException(submit.Name + "已经存在,并且没有指示为覆盖，因此不能保存，如果确认没有问题，请在保存的时候指定为覆盖。",
                        "工作流");
            }


            if (string.IsNullOrEmpty(submit.OnCompleteScript))
            {
                if (workProcess.OnComplete != null) workProcess.OnComplete = null;
            }
            else
            {
                workProcess.OnComplete ??= new WorkProcessScript();
                workProcess.OnComplete.Script = submit.OnCompleteScript;
            }


            var submitContext = new WorkflowSubmitContext(workProcess, _nodeStore);
            submitContext.OverWrite = submit.OverWrite;
            if (!submit.FillTo(submitContext, out var removNodes, out var error))
            {

                result.Message = error;
                return result.ToError("验证失败", 400);
            }

            _nodeStore.Remove(removNodes);
            _workProcessStore.AddOrUpdate(workProcess);
            _workProcessStore.SaveChangesAsync().Wait();

            //权限设置。检查原来的名字。因为Permission 只认name属性。
            WorkProcessPermission permission = null;
            if (!string.IsNullOrEmpty(workProcessSrcName))
            {
                permission = _workProcessPermissionStore.GetByProcessNameAsync(workProcessSrcName).Result;
                if (permission != null) permission.ProcessName = workProcess.Name;
            }

            permission ??= new WorkProcessPermission
            {
                ProcessName = workProcess.Name,
                ManageRoles = "admin"
            };
            _workProcessPermissionStore.AddOrUpdate(permission);

            transManager.Commit();

            ////拦截器
            //if (submit.Id == 0)
            //{
            //    submit.Id = workProcess.Id;
            //    _interceptorManager.OnWorkProcessCreator(workProcess);
            //}
            //else
            //{
            //    _interceptorManager.OnWorkProcessUpdate(workProcess);
            //}

            result.Id = workProcess.Id;
            result.Message = "保存成功";
            return result.ToSuccess();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存定义失败。");
            transManager.Rollback();
            throw;
        }
    }

    /// <summary>
    /// </summary>
    /// <param name="id"></param>
    /// <param name="enable"></param>
    /// <returns></returns>
    public WorkProcess Enable(in int id, bool enable)
    {
        var workProcess = _workProcessStore.Get(id);
        if (workProcess == null)
            throw new ArgumentOutOfRangeException(nameof(id), "找不id=" + id + "的流程定义。");
        var nodes = _nodeStore.GetNodesByWorkProcessAsync(workProcess).Result;
        var workflowSubmit = workProcess.ToSubmitViewModel(nodes);



        workProcess.Enable = enable;

        _workProcessStore.AddOrUpdate(workProcess);
        _workProcessStore.SaveChangesAsync().Wait();
        return workProcess;
    }

    /// <summary>
    /// </summary>
    /// <param name="id"></param>
    /// <param name="version"></param>
    /// <returns>如果版本存在</returns>
    public SwfResult<CheckVersionResult> CheckVersionExist(in int id, in int version)
    {
        var workProcess = id != 0 ? _workProcessStore.Get(id) : null;


        var result = new CheckVersionResult
        {
            ExistVersion = workProcess != null && workProcess.Version == version,
            Processing = _processInstanceStore.CountProcessingWorkflow(id)
        };
        return result.ToSuccess();
    }

    /// <summary>
    ///     删除工作流定义，会自动清除实例以及Tag，小心采用。
    /// </summary>
    /// <param name="id"></param>
    /// <param name="message"></param>
    /// <returns></returns>
    public bool TryDelete(int id, out string message)
    {
        var hasInstance = _processInstanceStore.CountProcessingWorkflow(id) > 0;
        if (hasInstance)
        {
            message = "已经有工作流实例存在，不能删除.";
            return false;
        }

        var workProcess = _workProcessStore.Get(id);
        var nodes = _nodeStore.GetNodesByWorkProcessAsync(workProcess).Result.ToArray();
        var instance = _processInstanceStore.ProcessInstances.Where(_ => _.WorkProcess == workProcess);


        var was = _workActivityStore.WorkActivities.Where(_ => instance.Contains(_.ProcessInstance));
        _workActivityStore.Remove(was);


        _processInstanceStore.Remove(instance);
        _workProcessStore.SaveChangesAsync().Wait();


        foreach (var node in nodes)
            switch (node)
            {
                case ConditionDecision codiNode:
                    codiNode.NextNode = null;
                    codiNode.Settings.Clear();

                    break;
                case SingleNode singleNode:
                    singleNode.NextNode = null;
                    singleNode.WorkProcess = null;
                    break;

                case ParallelNode sd:
                    sd.NextNodes.Clear();
                    sd.WorkProcess = null;
                    break;
            }


        foreach (var node in nodes)
        {
            _workTaskStore.Delete(node);
            _workProcessStore.SaveChangesAsync().Wait();
        }


        _workProcessStore.Delete(workProcess);
        _workProcessStore.SaveChangesAsync().Wait();
        message = "删除成功.";
        return true;
    }


    public async Task<SwfResult> SaveScript(WorkProcessScriptSubmit submit)
    {
        var wp = Get(submit.WorkProcessId);
        if (wp == null) return new SwfResult("没有找到ID为" + submit.WorkProcessId + "的工作流实例。", 0);

        try
        {
            switch (submit.WorkTaskSettingType)
            {
                case ScriptSettingType.WorkProcessComplete:
                    wp.OnComplete ??= new WorkProcessScript();
                    wp.OnComplete.Script = submit.Script;
                    break;

                case ScriptSettingType.WorkProcessStart:
                    wp.OnStart ??= new WorkProcessScript();
                    wp.OnStart.Script = submit.Script;
                    break;

                case ScriptSettingType.WorkProcessCancel:
                    wp.OnCancel ??= new WorkProcessScript();
                    wp.OnCancel.Script = submit.Script;
                    break;

                case ScriptSettingType.ScriptDecision:
                    var result = await HandleScriptDecisionAsync(wp, submit);
                    if (!result.Success) return result;
                    break;

                default:
                    var nodeResult = await HandleWorkTaskScriptAsync(wp, submit);
                    if (!nodeResult.Success) return nodeResult;
                    break;
            }

            _workProcessStore.AddOrUpdate(wp);
            await _workProcessStore.SaveChangesAsync().ConfigureAwait(false);

            return new SwfResult
            {
                Message = "成功保存脚本。",
                Code = 0
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存脚本失败，工作流ID: {WorkProcessId}, 脚本类型: {ScriptType}",
                submit.WorkProcessId, submit.WorkTaskSettingType);
            return new SwfResult("保存脚本时发生异常，请联系管理员。", -1);
        }
    }

    private async Task<SwfResult> HandleScriptDecisionAsync(WorkProcess wp, WorkProcessScriptSubmit submit)
    {
        var decisionNode = await _nodeStore.GetByNodeAsync(wp, submit.Node).ConfigureAwait(false);
        if (decisionNode == null)
            return new SwfResult
            {
                Message = $"节点{submit.Node}无法在工作流{wp.Name}中找到",
                Code = -1
            };

        if (decisionNode is BoolScriptDecision decision)
        {
            decision.Script = submit.Script;
            _nodeStore.AddOrUpdate(decisionNode);
            return new SwfResult { Code = 0 };
        }

        return new SwfResult
        {
            Message = $"节点{submit.Node}并不是脚本判断器。",
            Code = -1
        };
    }

    private async Task<SwfResult> HandleWorkTaskScriptAsync(WorkProcess wp, WorkProcessScriptSubmit submit)
    {
        var node = await _nodeStore.GetByNodeAsync(wp, submit.Node).ConfigureAwait(false);
        if (node == null)
            return new SwfResult
            {
                Message = $"{submit.Node}不存在工作流{wp.Name}",
                Code = -1
            };

        if (node is not WorkTask workTask)
            return new SwfResult
            {
                Message = $"节点{submit.Node}不是工作任务类型",
                Code = -1
            };

        switch (submit.WorkTaskSettingType)
        {
            case ScriptSettingType.WorkActivityComplete:
                workTask.WorkActivityCompleteScript ??= new WorkActivityScript();
                workTask.WorkActivityCompleteScript.Script = submit.Script;
                break;

            case ScriptSettingType.WorkTaskAssigner:
                if (workTask.Assigner is ScriptAssigner scriptAssigner)
                    scriptAssigner.Script = submit.Script;
                else
                    return new SwfResult("当前工作任务的分配器不是脚本类型", -1);
                break;

            case ScriptSettingType.WorkTaskComplete:
                workTask.WorkTaskCompleteScript ??= new WorkTaskCompleteScript();
                workTask.WorkTaskCompleteScript.Script = submit.Script;
                break;

            case ScriptSettingType.WorkTaskStart:
                workTask.WorkTaskStartScript ??= new WorkTaskStartScript();
                workTask.WorkTaskStartScript.Script = submit.Script;
                break;

            case ScriptSettingType.WorkTaskCommand:
                var command = workTask.Commands.FirstOrDefault(c => c.Name == submit.CommandName);
                if (command == null)
                {
                    command = new WorkTaskScriptCommand
                    {
                        Name = submit.CommandName,
                        Script = submit.Script
                    };
                    workTask.Commands.Add(command);
                }
                else if (command is WorkTaskScriptCommand scriptCommand)
                {
                    scriptCommand.Script = submit.Script;
                }
                else
                {
                    return new SwfResult($"命令{submit.CommandName}不是脚本类型", -1);
                }

                break;

            default:
                return new SwfResult($"不支持的脚本设置类型: {submit.WorkTaskSettingType}", -1);
        }

        _nodeStore.AddOrUpdate(node);
        return new SwfResult { Code = 0 };
    }
}