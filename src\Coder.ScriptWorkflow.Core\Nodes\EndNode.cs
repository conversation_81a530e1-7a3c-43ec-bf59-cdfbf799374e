﻿using Coder.ScriptWorkflow.Nodes.Transitions;

namespace Coder.ScriptWorkflow.Nodes;

/// <summary>
///     结束节点。
/// </summary>
public class EndNode : Node
{
    /// <inheritdoc />
    public EndNode()
    {
        Transition = new EmptyTransition();
    }

    /// <inheritdoc />
    public EndNode(WorkProcess wp) : base(wp)
    {
        Transition = new EmptyTransition();
    }

    /// <inheritdoc />
    public override string Name
    {
        get => "结束";
        set { }
    }

    /// <inheritdoc />
    public override bool Auto { get; set; } = false;

    /// <inheritdoc />
    /// <summary>
    ///     获取下一个节点。
    /// </summary>
    /// <param name="workflowContext">工作流上下文</param>
    /// <param name="nextNode">下一个节点</param>
    /// <returns>结束节点没有下一个节点，始终返回false</returns>
    public override bool TryNextNode(IWorkflowContext workflowContext, out Node nextNode)
    {
        nextNode = null;
        return false;
    }

    /// <inheritdoc />
    public override void Validate()
    {
        // 结束节点不需要验证
    }
}