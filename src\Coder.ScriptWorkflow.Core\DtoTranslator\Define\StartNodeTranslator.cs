﻿using Coder.ScriptWorkflow.Nodes;
using Coder.ScriptWorkflow.Stores;
using Coder.ScriptWorkflow.ViewModels.Defined;

namespace Coder.ScriptWorkflow.DtoTranslator.Define;

internal class StartNodeTranslator : SingleNodeTranslator<StartNodeSubmit, StartNode>
{
    protected override void FillToEntity(StartNode node, StartNodeSubmit submit, WorkflowSubmitContext context, INodeStore nodeStore)
    {
    }

    protected override void FillToViewModel(StartNode src, StartNodeSubmit target)
    {
        target.NextNodeName = src.NextNode.Name;
    }
}