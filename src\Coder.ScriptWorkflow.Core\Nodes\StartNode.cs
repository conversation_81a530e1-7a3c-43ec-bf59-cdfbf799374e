﻿using System;
using Coder.ScriptWorkflow.ViewModels.Defined;
using Coder.ScriptWorkflow.Nodes.Transitions;

namespace Coder.ScriptWorkflow.Nodes;

/// <summary>
///     开始节点
/// </summary>
public class StartNode : SingleNode
{
    /// <summary>
    /// </summary>
    public StartNode()
    {
    }

    /// <summary>
    /// </summary>
    /// <param name="nextNode">下一个节点</param>
    /// <param name="wp">工作流定义</param>
    /// <exception cref="ArgumentNullException"></exception>
    public StartNode(Node nextNode, WorkProcess wp) : base(wp)
    {
        Transition = new SingleNodeTransition(nextNode ?? throw new ArgumentNullException(nameof(nextNode)));
    }

    /// <inheritdoc />
    public override bool Auto
    {
        get => true;
        set
        {
            // throw new ArgumentOutOfRangeException(nameof(value), "StartNode不能设置自动。");
        }
    }

    /// <inheritdoc />
    public override string Name
    {
        get => StartNodeSubmit.StartNodeName;
        set { }
    }
}