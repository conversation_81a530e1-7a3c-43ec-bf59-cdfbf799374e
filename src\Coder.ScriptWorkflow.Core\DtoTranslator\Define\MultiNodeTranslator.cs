﻿using Coder.ScriptWorkflow.Nodes;
using Coder.ScriptWorkflow.ViewModels.Defined;

namespace Coder.ScriptWorkflow.DtoTranslator.Define;

public abstract class MultiNodeTranslator<TNodeSubmit, TNode> : NodeTranslator<TNodeSubmit, TNode>
    where TNodeSubmit : ParallelNodeSubmit, new()
    where TNode : ParallelNode, new()
{
    public override bool Validate(TNodeSubmit nodeSubmit, out string errorMessage)
    {
        if (nodeSubmit.NextNodeNames == null || nodeSubmit.NextNodeNames.Length == 0)
        {
            errorMessage = "至少包含一个节点";
            return false;
        }

        errorMessage = null;
        return true;
    }
}