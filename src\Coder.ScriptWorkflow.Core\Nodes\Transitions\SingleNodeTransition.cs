using System;
using System.Collections.Generic;

namespace Coder.ScriptWorkflow.Nodes.Transitions;

/// <summary>
///     简单节点转换器 - 处理单一后继节点的场景
/// </summary>
public class SingleNodeTransition : INodeTransition
{
    private Node _nextNode;

    /// <summary>
    ///     构造函数
    /// </summary>
    public SingleNodeTransition()
    {
    }

    /// <summary>
    ///     构造函数
    /// </summary>
    /// <param name="nextNode">下一个节点</param>
    public SingleNodeTransition(Node nextNode)
    {
        _nextNode = nextNode ?? throw new ArgumentNullException(nameof(nextNode));
    }

    /// <summary>
    ///     下一个节点
    /// </summary>
    public Node NextNode
    {
        get => _nextNode;
        set => _nextNode = value ?? throw new ArgumentNullException(nameof(value));
    }

    /// <inheritdoc />
    public bool TryGetNextNode(Node currentNode, IWorkflowContext workflowContext, out Node nextNode)
    {
        if (_nextNode == null)
        {
            nextNode = null;
            return false;
        }

        nextNode = _nextNode;
        return true;
    }

    /// <inheritdoc />
    public IEnumerable<Node> GetNextNodes(Node currentNode, IWorkflowContext workflowContext)
    {
        if (_nextNode != null)
        {
            yield return _nextNode;
        }
    }

    /// <inheritdoc />
    public void Validate(Node currentNode)
    {
        if (_nextNode == null)
        {
            throw new WorkflowDefinedException($"节点 '{currentNode.Name}' 必须指定下一个节点", currentNode.Name);
        }
    }
}
