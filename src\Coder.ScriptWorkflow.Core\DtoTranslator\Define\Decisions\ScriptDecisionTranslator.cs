﻿using Coder.ScriptWorkflow.Decisions.BooleanDecisions;
using Coder.ScriptWorkflow.Stores;
using Coder.ScriptWorkflow.ViewModels.Defined;
using NiL.JS;
using System;

namespace Coder.ScriptWorkflow.DtoTranslator.Define.Decisions;

/// <summary>
/// </summary>
internal class ScriptDecisionTranslator : SingleNodeTranslator<BooleanScriptDecisionSubmit, BoolScriptDecision>
{
    /// <summary>
    /// </summary>
    /// <param name="node"></param>
    /// <param name="submit"></param>
    /// <param name="context"></param>
    /// <param name="nodeStore"></param>
    protected override void FillToEntity(BoolScriptDecision node, BooleanScriptDecisionSubmit submit,
        WorkflowSubmitContext context, INodeStore nodeStore)
    {
        node.Script = submit.Script;
        node.ElseDescription = submit.ElseDescription;
        node.MatchDescription = submit.MatchDescription;
    }

    /// <summary>
    /// </summary>
    /// <param name="src"></param>
    /// <param name="target"></param>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    protected override void FillToViewModel(BoolScriptDecision src, BooleanScriptDecisionSubmit target)
    {
        var decision = src;
        if (decision == null)
            throw new ArgumentOutOfRangeException(nameof(src), "node 必须是ScriptDecision");
        target.ElseNodeName = decision?.ElseNode?.Name;
        target.Script = decision.Script;
        target.ElseDescription = decision.ElseDescription;
        target.MatchDescription = decision.MatchDescription;
    }

    /// <summary>
    /// </summary>
    /// <param name="context"></param>
    /// <param name="node"></param>
    /// <param name="nodeSubmit"></param>
    protected override void BuildEntityRelation(WorkflowSubmitContext context, BoolScriptDecision node,
        BooleanScriptDecisionSubmit nodeSubmit)
    {
        base.BuildEntityRelation(context, node, nodeSubmit);
        if (context.WorkProcess.Enable && string.IsNullOrEmpty(nodeSubmit.ElseNodeName))
            throw new WorkflowDefinedException("需要设置‘否决节点’", nodeSubmit.Name);
        if (!string.IsNullOrEmpty(nodeSubmit.ElseNodeName))
            node.ElseNode = context.GetNode(nodeSubmit.ElseNodeName);
    }

    public override bool Validate(BooleanScriptDecisionSubmit nodeSubmit, out string errorMessage)
    {
        if (!base.Validate(nodeSubmit, out errorMessage))
            return false;

        var decision = $"判断器{nodeSubmit.Name}";
        if (!base.Validate(nodeSubmit, out var msg))
        {
            errorMessage = decision + msg;
            return false;
        }

        errorMessage = null;
        if (string.IsNullOrEmpty(nodeSubmit.Script))
        {
            errorMessage = $"[{decision}]脚本必须填写";
            return false;
        }

        if (nodeSubmit.ElseNodeName == null)
        {
            errorMessage = $"[{decision}]否决节点必须填写。";
            return false;
        }


        return true;
    }
}