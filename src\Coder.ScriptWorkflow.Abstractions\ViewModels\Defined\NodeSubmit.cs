﻿using System;
using Coder.ScriptWorkflow.ViewModels.Defined.JsonConverts;
using Newtonsoft.Json;

namespace Coder.ScriptWorkflow.ViewModels.Defined;

/// <summary>
///     节点提交
/// </summary>
[JsonConverter(typeof(NodeSubmitJsonConverter))]
public abstract class NodeSubmit
{
    /// <summary>
    /// </summary>
    protected NodeSubmit()
    {
    }

    /// <summary>
    ///     类型
    /// </summary>
    [JsonProperty("$type")]
    public string Type { get; set; }

    /// <summary>
    ///     id
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    ///     节点名称
    /// </summary>
    public string Name { get; set; }


}