﻿using Coder.ScriptWorkflow.Decisions.BooleanDecisions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Coder.ScriptWorkflow.Mapping;

internal class ConditionSettingMapping : IEntityTypeConfiguration<ConditionSetting>
{
    private readonly string _prefix;

    /// <summary>
    /// </summary>
    /// <param name="prefix"></param>
    public ConditionSettingMapping(string prefix)
    {
        _prefix = prefix;
    }

    public void Configure(EntityTypeBuilder<ConditionSetting> builder)
    {
        builder.HasKey(_ => _.Id);
        builder.Property(_ => _.Id).ValueGeneratedOnAdd();
        builder.ToTable($"{_prefix}_condition_decision");
        builder.Property(decision => decision.MatchValue).HasMaxLength(100).HasComment("匹配的字符串");
        builder.Property(decision => decision.Description).HasMaxLength(300);
        builder.HasOne(conditionSetting => conditionSetting.Node);
    }
}