using System;
using System.Collections.Generic;
using System.Linq;

namespace Coder.ScriptWorkflow.Nodes.Transitions;

/// <summary>
///     并行分支转换器 - 处理并行分支节点的多个后继节点
/// </summary>
public class ParallelNodesTransition : INodeTransition
{
    private readonly List<Node> _nextNodes = new();

    /// <summary>
    ///     构造函数
    /// </summary>
    public ParallelNodesTransition()
    {
    }

    /// <summary>
    ///     构造函数
    /// </summary>
    /// <param name="nextNodes">下一个节点集合</param>
    public ParallelNodesTransition(IEnumerable<Node> nextNodes)
    {
        if (nextNodes != null)
        {
            foreach (var node in nextNodes)
            {
                AddNextNode(node);
            }
        }
    }

    /// <summary>
    ///     下一个节点集合
    /// </summary>
    public IReadOnlyList<Node> NextNodes => _nextNodes.AsReadOnly();

    /// <summary>
    ///     添加下一个节点
    /// </summary>
    /// <param name="node">要添加的节点</param>
    public void AddNextNode(Node node)
    {
        if (node == null)
            throw new ArgumentNullException(nameof(node));
            
        if (!_nextNodes.Contains(node))
        {
            _nextNodes.Add(node);
        }
    }

    /// <summary>
    ///     移除下一个节点
    /// </summary>
    /// <param name="node">要移除的节点</param>
    public void RemoveNextNode(Node node)
    {
        if (node != null)
        {
            _nextNodes.Remove(node);
        }
    }

    /// <inheritdoc />
    public bool TryGetNextNode(Node currentNode, IWorkflowContext workflowContext, out Node nextNode)
    {
        // 并行分支节点不通过单一后继节点方式获取，返回false表示需要特殊处理
        nextNode = null;
        return false;
    }

    /// <inheritdoc />
    public IEnumerable<Node> GetNextNodes(Node currentNode, IWorkflowContext workflowContext)
    {
        return _nextNodes;
    }

    /// <inheritdoc />
    public void Validate(Node currentNode)
    {
        if (!_nextNodes.Any())
        {
            throw new WorkflowDefinedException($"并行分支节点 '{currentNode.Name}' 必须至少有一个下一个节点", currentNode.Name);
        }

        // 验证所有下一个节点都是WorkTask
        var invalidNodes = _nextNodes.Where(n => !(n is WorkTask)).ToList();
        if (invalidNodes.Any())
        {
            var invalidNodeNames = string.Join(", ", invalidNodes.Select(n => n.Name));
            throw new WorkflowDefinedException($"并行分支节点 '{currentNode.Name}' 的下一个节点必须都是WorkTask，但发现非WorkTask节点: {invalidNodeNames}", currentNode.Name);
        }
    }
}
