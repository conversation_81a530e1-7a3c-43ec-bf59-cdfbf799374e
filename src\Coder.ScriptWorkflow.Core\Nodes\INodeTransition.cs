﻿using System.Collections.Generic;

namespace Coder.ScriptWorkflow.Nodes;

/// <summary>
///     节点转换器接口
/// </summary>
public interface INodeTransition
{
    /// <summary>
    ///     尝试获取下一个节点
    /// </summary>
    /// <param name="currentNode">当前节点</param>
    /// <param name="workflowContext">工作流上下文</param>
    /// <param name="nextNode">下一个节点</param>
    /// <returns>是否成功获取下一个节点</returns>
    bool TryGetNextNode(Node currentNode, IWorkflowContext workflowContext, out Node nextNode);

    /// <summary>
    ///     获取所有可能的下一节点
    /// </summary>
    /// <param name="currentNode">当前节点</param>
    /// <param name="workflowContext">工作流上下文</param>
    /// <returns>下一节点集合</returns>
    IEnumerable<Node> GetNextNodes(Node currentNode, IWorkflowContext workflowContext);

    /// <summary>
    ///     验证转换器配置
    /// </summary>
    /// <param name="currentNode">当前节点</param>
    void Validate(Node currentNode);
}