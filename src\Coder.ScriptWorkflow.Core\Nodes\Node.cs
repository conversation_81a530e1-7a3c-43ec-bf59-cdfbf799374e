﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace Coder.ScriptWorkflow.Nodes;

/// <summary>
///     节点。
/// </summary>
public abstract class Node
{
    protected Node()
    {
    }

    protected Node(WorkProcess wp)
    {
        WorkProcess = wp ?? throw new ArgumentNullException(nameof(wp));
    }

    /// <summary>
    ///     名称
    /// </summary>
    public virtual string Name { get; set; }

    /// <summary>
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    ///     是否自动下一层
    /// </summary>
    public abstract bool Auto { get; set; }

    /// <summary>
    ///     工作流定义
    /// </summary>
    public virtual WorkProcess WorkProcess { get; set; }

    /// <summary>
    ///     节点转换器，处理节点间的转换逻辑
    /// </summary>
    public virtual INodeTransition Transition { get; set; }

    /// <summary>
    ///     获取下一个节点。
    /// </summary>
    /// <param name="workflowContext">工作流上下文</param>
    /// <param name="nextNode">下一个节点</param>
    /// <returns>是否成功获取下一个节点</returns>
    /// <exception cref="WorkflowDefinedException"></exception>
    public virtual bool TryNextNode(IWorkflowContext workflowContext, out Node nextNode)
    {
        if (Transition == null)
        {
            throw new WorkflowDefinedException($"节点 '{Name}' 没有配置转换器", Name);
        }
        
        return Transition.TryGetNextNode(this, workflowContext, out nextNode);
    }

    /// <summary>
    ///     获取所有可能的下一节点（用于并行分支等场景）
    /// </summary>
    /// <param name="workflowContext">工作流上下文</param>
    /// <returns>下一节点集合</returns>
    public virtual IEnumerable<Node> GetNextNodes(IWorkflowContext workflowContext)
    {
        if (Transition == null)
        {
            return Enumerable.Empty<Node>();
        }
        
        return Transition.GetNextNodes(this, workflowContext);
    }

    /// <summary>
    ///     验证节点配置
    /// </summary>
    public virtual void Validate()
    {
        if (Transition == null)
        {
            throw new WorkflowDefinedException($"节点 '{Name}' 必须配置转换器", Name);
        }
        
        Transition.Validate(this);
    }

    /// <summary>
    ///     位置信息
    /// </summary>
    public Position Position { get; set; } = new Position();
}

/// <summary>
///     位置信息
/// </summary>
public class Position
{
    public int X { get; set; }
    public int Y { get; set; }
}
