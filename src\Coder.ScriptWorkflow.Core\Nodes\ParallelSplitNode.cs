using System;
using System.Collections.Generic;
using System.Linq;
using Coder.ScriptWorkflow.Nodes.Transitions;

namespace Coder.ScriptWorkflow.Nodes;

/// <summary>
/// 并行分支节点 - 将流程分发到多个并行的WorkTask
/// </summary>
public class ParallelSplitNode : ParallelNode
{
    /// <summary>
    /// 构造函数
    /// </summary>
    public ParallelSplitNode()
    {
        Transition = new ParallelNodesTransition();
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="workProcess">工作流定义</param>
    public ParallelSplitNode(WorkProcess workProcess) : base(workProcess)
    {
        Transition = new ParallelNodesTransition();
    }

    /// <summary>
    /// 并行分支节点通常不是自动执行的，需要手动触发分支
    /// </summary>
    public override bool Auto { get; set; } = false;

    /// <summary>
    /// 获取下一个节点 - 对于并行分支节点，这个方法返回false，
    /// 因为需要特殊处理多个下一个节点
    /// </summary>
    /// <param name="workflowContext">工作流上下文</param>
    /// <param name="nextNode">下一个节点（对于并行分支，这里返回null）</param>
    /// <returns>返回false表示需要特殊处理</returns>
    public override bool TryNextNode(IWorkflowContext workflowContext, out Node nextNode)
    {
        // 并行分支节点不通过单一后继节点方式获取，返回false表示需要特殊处理
        nextNode = null;
        return false;
    }

    /// <summary>
    /// 获取所有下一个节点
    /// </summary>
    /// <returns>下一个节点列表</returns>
    public IEnumerable<Node> GetNextNodes() => GetNextNodes(null);

    /// <summary>
    /// 添加下一个节点
    /// </summary>
    /// <param name="node">要添加的节点</param>
    public void AddNextNode(Node node)
    {
        if (Transition is ParallelNodesTransition parallelTransition)
        {
            parallelTransition.AddNextNode(node);
        }
        else
        {
            throw new InvalidOperationException("并行分支节点的转换器类型不正确");
        }
    }

    /// <summary>
    /// 移除下一个节点
    /// </summary>
    /// <param name="node">要移除的节点</param>
    public void RemoveNextNode(Node node)
    {
        if (Transition is ParallelNodesTransition parallelTransition)
        {
            parallelTransition.RemoveNextNode(node);
        }
    }

    /// <summary>
    /// 验证节点配置
    /// </summary>
    public override void Validate()
    {
        base.Validate();
        
        if (Transition is not ParallelNodesTransition)
        {
            throw new WorkflowDefinedException($"并行分支节点 '{Name}' 必须使用 ParallelSplitTransition 转换器", Name);
        }
    }
}
