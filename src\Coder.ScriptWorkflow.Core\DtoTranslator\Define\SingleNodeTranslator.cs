﻿using Coder.ScriptWorkflow.Nodes;
using Coder.ScriptWorkflow.ViewModels.Defined;

namespace Coder.ScriptWorkflow.DtoTranslator.Define;

public abstract class SingleNodeTranslator<TNodeSubmit, TNode> : NodeTranslator<TNodeSubmit, TNode>
    where TNodeSubmit : SingleNodeSubmit, new()
    where TNode : SingleNode, new()
{
    /// <summary>
    /// </summary>
    /// <param name="nodeSubmit"></param>
    /// <param name="errorMessage"></param>
    /// <returns></returns>
    public override bool Validate(TNodeSubmit nodeSubmit, out string errorMessage)
    {
        errorMessage = null;
        if (string.IsNullOrWhiteSpace(nodeSubmit.NextNodeName))
        {
            errorMessage = "需要设置节点的下一个节点。";
            return false;
        }

        return true;
    }

    /// <summary>
    ///     创建节点之间的关系。
    /// </summary>
    /// <param name="context"></param>
    /// <param name="node"></param>
    /// <param name="nodeSubmit"></param>
    protected override void BuildEntityRelation(WorkflowSubmitContext context, TNode node, TNodeSubmit nodeSubmit)
    {
        if (context.WorkProcess.Enable && string.IsNullOrEmpty(nodeSubmit.NextNodeName))
            throw new WorkflowDefinedException(nodeSubmit.Name + "需要设置‘下一个节点’");

        if (nodeSubmit.NextNodeName != null)
            node.NextNode = context.GetNode(nodeSubmit.NextNodeName);
    }
}