﻿using Coder.ScriptWorkflow.Nodes;
using Innofactor.EfCoreJsonValueConverter;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Coder.ScriptWorkflow.Mapping;

/// <summary>
/// </summary>
internal class NodeMapping : IEntityTypeConfiguration<Node>
{
    private readonly string _prefix;

    /// <summary>
    /// </summary>
    /// <param name="prefix"></param>
    public NodeMapping(string prefix)
    {
        _prefix = prefix;
    }

    /// <summary>
    /// </summary>
    /// <param name="builder"></param>
    public void Configure(EntityTypeBuilder<Node> builder)
    {
        builder.HasKey(n => n.Id);
        builder.Property(node => node.Id).ValueGeneratedOnAdd();
        builder.HasDiscriminator();
        builder.HasOne(_ => _.WorkProcess).WithMany().OnDelete(DeleteBehavior.SetNull);

        builder.Property(node => node.Position).HasJsonValueConversion();

        builder.ToTable($"{_prefix}_node");
    }
}
internal class SingleNodeMapping : IEntityTypeConfiguration<SingleNode>
{
   

    /// <summary>
    /// </summary>
    /// <param name="builder"></param>
    public void Configure(EntityTypeBuilder<SingleNode> builder)
    {

        builder.HasBaseType<Node>();
        builder.HasOne(singleNode => singleNode.NextNode);
    }
}

internal class ParallelNodeMapping : IEntityTypeConfiguration<ParallelNode>
{
    /// <summary>
    /// </summary>
    /// <param name="builder"></param>
    public void Configure(EntityTypeBuilder<ParallelNode> builder)
    {

        builder.HasBaseType<Node>();
        builder.HasMany(singleNode => singleNode.NextNodes);
    }
}