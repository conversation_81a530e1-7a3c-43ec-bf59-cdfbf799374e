using System.Collections.Generic;
using System.Linq;

namespace Coder.ScriptWorkflow.Nodes.Transitions;

/// <summary>
///     空转换器 - 用于不需要转换的节点（如结束节点）
/// </summary>
public class EmptyTransition : INodeTransition
{
    /// <inheritdoc />
    public bool TryGetNextNode(Node currentNode, IWorkflowContext workflowContext, out Node nextNode)
    {
        nextNode = null;
        return false;
    }

    /// <inheritdoc />
    public IEnumerable<Node> GetNextNodes(Node currentNode, IWorkflowContext workflowContext)
    {
        return Enumerable.Empty<Node>();
    }

    /// <inheritdoc />
    public void Validate(Node currentNode)
    {
        // 空转换器不需要验证
    }
}
