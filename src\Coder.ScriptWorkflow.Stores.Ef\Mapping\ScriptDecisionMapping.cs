﻿using Coder.ScriptWorkflow.Decisions;
using Coder.ScriptWorkflow.Decisions.BooleanDecisions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Coder.ScriptWorkflow.Mapping;

/// <summary>
/// </summary>
internal class ScriptDecisionMapping : IEntityTypeConfiguration<BoolScriptDecision>
{
    /// <summary>
    /// </summary>
    /// <param name="builder"></param>
    public void Configure(EntityTypeBuilder<BoolScriptDecision> builder)
    {
        builder.HasBaseType<Decision>();
        builder.Property(_ => _.Script).HasColumnName("Script").HasColumnType("text").HasComment("执行脚本");
        ;
        builder.HasOne(_ => _.ElseNode);
        builder.Property(_ => _.ElseDescription).HasMaxLength(20);
    }
}