using System;
using System.Collections.Generic;

namespace Coder.ScriptWorkflow.Nodes.Transitions;

/// <summary>
///     节点转换器工厂
/// </summary>
public static class NodeTransitionFactory
{
    /// <summary>
    ///     创建简单转换器
    /// </summary>
    /// <param name="nextNode">下一个节点</param>
    /// <returns>简单转换器实例</returns>
    public static SingleNodeTransition CreateSimple(Node nextNode)
    {
        return new SingleNodeTransition(nextNode);
    }

    /// <summary>
    ///     创建并行分支转换器
    /// </summary>
    /// <param name="nextNodes">下一个节点集合</param>
    /// <returns>并行分支转换器实例</returns>
    public static ParallelNodesTransition CreateParallelSplit(IEnumerable<Node> nextNodes = null)
    {
        return new ParallelNodesTransition(nextNodes);
    }

    /// <summary>
    ///     创建条件转换器
    /// </summary>
    /// <param name="conditions">转换条件集合</param>
    /// <param name="defaultNextNode">默认下一个节点</param>
    /// <returns>条件转换器实例</returns>
    public static ConditionalTransition CreateConditional(IEnumerable<TransitionCondition> conditions = null, Node defaultNextNode = null)
    {
        var transition = new ConditionalTransition(conditions);
        if (defaultNextNode != null)
        {
            transition.DefaultNextNode = defaultNextNode;
        }
        return transition;
    }

    /// <summary>
    ///     创建空转换器
    /// </summary>
    /// <returns>空转换器实例</returns>
    public static EmptyTransition CreateEmpty()
    {
        return new EmptyTransition();
    }

    /// <summary>
    ///     根据节点类型创建默认转换器
    /// </summary>
    /// <param name="nodeType">节点类型</param>
    /// <returns>默认转换器实例</returns>
    public static INodeTransition CreateDefault(Type nodeType)
    {
        if (nodeType == typeof(EndNode))
        {
            return CreateEmpty();
        }
        
        if (nodeType == typeof(ParallelSplitNode))
        {
            return CreateParallelSplit();
        }
        
        // 其他节点类型默认使用简单转换器
        return null; // 需要外部设置
    }
}
