﻿using System;
using System.Linq;
using Coder.ScriptWorkflow.Decisions.BooleanDecisions;
using Coder.ScriptWorkflow.Stores;
using Coder.ScriptWorkflow.ViewModels.Defined;

namespace Coder.ScriptWorkflow.DtoTranslator.Define.Decisions;

internal class ConditionDecisionTranslator : SingleNodeTranslator<ConditionDecisionSubmit, ConditionDecision>
{
    private readonly Random _random = new();

    protected override void FillToEntity(ConditionDecision node, ConditionDecisionSubmit submit,
        WorkflowSubmitContext context,
        INodeStore nodeStore)
    {
        node.Script = submit.Script;
        node.MatchDescription = submit.MatchDescription;

        foreach (var conditionSettingSubmit in submit.Settings)
        {
            var nodeSetting = (conditionSettingSubmit.Id != 0
                                  ? node.Settings.FirstOrDefault(x => x.Id == conditionSettingSubmit.Id)
                                  : new ConditionSetting()) ??
                              new ConditionSetting();
            //新建setting 在设置普通值的时候，设置id为负数,强制低实现 submit和entity之间的id一致，
            //方便 在buildEntityRelation的时候，获取setting，并且设置node
            if (nodeSetting.Id == 0)
            {
                nodeSetting.Id = _random.Next(-1, -99999999);
                conditionSettingSubmit.Id = nodeSetting.Id;
            }

            nodeSetting.Description = conditionSettingSubmit.Description;
            nodeSetting.MatchValue = conditionSettingSubmit.MatchValue;
            node.Settings.Add(nodeSetting);
        }
    }

    protected override void FillToViewModel(ConditionDecision src, ConditionDecisionSubmit target)
    {
        foreach (var setting in src.Settings)
            target.Settings.Add(new ConditionSettingSubmit
            {
                Description = setting.Description,
                Id = setting.Id,
                NodeName = setting?.Node?.Name,
                MatchValue = setting.MatchValue
            });

        target.Script = src.Script;
        target.MatchDescription = src.MatchDescription;
        target.NextNodeName = src.NextNode.Name;
        target.Id = src.Id;
        target.Name = src.Name;
    }

    /// <summary>
    /// </summary>
    /// <param name="context"></param>
    /// <param name="node"></param>
    /// <param name="nodeSubmit"></param>
    protected override void BuildEntityRelation(WorkflowSubmitContext context, ConditionDecision node,
        ConditionDecisionSubmit nodeSubmit)
    {
        base.BuildEntityRelation(context, node, nodeSubmit);

        foreach (var settingSubmit in nodeSubmit.Settings)
        {
            var settingEntity = node.Settings.First(x => x.Id == settingSubmit.Id);
            settingEntity.Node = context.GetNode(settingSubmit.NodeName);

            if (settingEntity.Id < 0)
            {
                settingEntity.Id = 0;
                settingSubmit.Id = 0;
            }
        }
    }
}