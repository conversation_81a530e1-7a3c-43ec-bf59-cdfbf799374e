using System.Linq;
using Coder.ScriptWorkflow.Nodes;
using Coder.ScriptWorkflow.Stores;
using Coder.ScriptWorkflow.ViewModels.Defined;

namespace Coder.ScriptWorkflow.DtoTranslator.Define;

/// <summary>
/// 并行分支节点转换器
/// </summary>
internal class ParallelSplitNodeTranslator : MultiNodeTranslator<ParallelSplitNodeSubmit, ParallelSplitNode>
{
    /// <summary>
    /// 将DTO数据填充到实体
    /// </summary>
    /// <param name="node">并行分支节点实体</param>
    /// <param name="submit">并行分支节点DTO</param>
    /// <param name="context">工作流提交上下文</param>
    /// <param name="nodeStore">节点存储</param>
    protected override void FillToEntity(ParallelSplitNode node, ParallelSplitNodeSubmit submit, WorkflowSubmitContext context, INodeStore nodeStore)
    {
        // 清空现有的下一个节点列表
        node.NextNodes.Clear();
        
        // 根据节点名称添加下一个节点
        foreach (var nextNodeName in submit.NextNodeNames)
        {
            if (!string.IsNullOrWhiteSpace(nextNodeName))
            {
                var nextNode = context.GetNode(nextNodeName);
                if (nextNode != null)
                {
                    node.AddNextNode(nextNode);
                }
            }
        }
    }

    /// <summary>
    /// 将实体数据填充到DTO
    /// </summary>
    /// <param name="src">并行分支节点实体</param>
    /// <param name="target">并行分支节点DTO</param>
    protected override void FillToViewModel(ParallelSplitNode src, ParallelSplitNodeSubmit target)
    {
        target.NextNodeNames = src.NextNodes.Select(n => n.Name).ToArray();
    }

    /// <summary>
    /// 构建节点关系
    /// </summary>
    /// <param name="context">工作流提交上下文</param>
    /// <param name="node">节点</param>
    /// <param name="nodeSubmit">节点DTO</param>
    protected override void BuildEntityRelation(WorkflowSubmitContext context, ParallelSplitNode node, ParallelSplitNodeSubmit nodeSubmit)
    {
        // 对于并行分支节点，不使用单个NextNode，而是使用NextNodes列表
        // 在FillToEntity中已经处理了NextNodes的关系
        
        // 验证至少有一个下一个节点
        if (!nodeSubmit.NextNodeNames.Any())
        {
            throw new WorkflowDefinedException($"并行分支节点 '{nodeSubmit.Name}' 必须至少指定一个下一个节点");
        }
    }
}
