using System;
using System.Collections.Generic;
using System.Linq;
using Coder.ScriptWorkflow.Nodes.Transitions;

namespace Coder.ScriptWorkflow.Nodes;

/// <summary>
///     汇聚节点 - 等待多个并行分支完成后继续
/// </summary>
public class ParallelJoinNode : SingleNode
{
    private bool _auto = true;

    /// <summary>
    ///     构造函数
    /// </summary>
    public ParallelJoinNode()
    {
    }

    /// <summary>
    ///     构造函数
    /// </summary>
    /// <param name="workProcess">工作流定义</param>
    public ParallelJoinNode(WorkProcess workProcess) : base(workProcess)
    {
    }

    /// <summary>
    ///     需要等待的前置WorkTask名称列表
    /// </summary>
    public List<string> WaitForWorkTasks { get; set; } = new();

    /// <summary>
    ///     汇聚条件：All(所有完成) 或 Any(任一完成) 或 Custom(自定义脚本)
    /// </summary>
    public JoinCondition JoinCondition { get; set; } = JoinCondition.All;

    /// <summary>
    ///     当JoinCondition为Custom时使用的自定义脚本
    /// </summary>
    public string CustomJoinScript { get; set; }

    /// <summary>
    ///     汇聚节点通常是自动执行的，当条件满足时自动继续
    /// </summary>
    public override bool Auto
    {
        get => true;
        set { }
    }

    /// <summary>
    ///     添加需要等待的WorkTask
    /// </summary>
    /// <param name="workTaskName">WorkTask名称</param>
    public void AddWaitForWorkTask(string workTaskName)
    {
        if (string.IsNullOrWhiteSpace(workTaskName))
            throw new ArgumentException("WorkTask名称不能为空", nameof(workTaskName));

        if (!WaitForWorkTasks.Contains(workTaskName)) WaitForWorkTasks.Add(workTaskName);
    }

    /// <summary>
    ///     移除需要等待的WorkTask
    /// </summary>
    /// <param name="workTaskName">WorkTask名称</param>
    public void RemoveWaitForWorkTask(string workTaskName)
    {
        if (!string.IsNullOrWhiteSpace(workTaskName)) WaitForWorkTasks.Remove(workTaskName);
    }

    /// <summary>
    ///     验证节点配置
    /// </summary>
    public override void Validate()
    {
        base.Validate();

        if (!WaitForWorkTasks.Any()) throw new WorkflowDefinedException($"汇聚节点 '{Name}' 必须至少指定一个需要等待的WorkTask", Name);

        if (JoinCondition == JoinCondition.Custom && string.IsNullOrWhiteSpace(CustomJoinScript))
            throw new WorkflowDefinedException($"汇聚节点 '{Name}' 使用自定义汇聚条件时必须提供CustomJoinScript", Name);

        if (Transition == null) throw new WorkflowDefinedException($"汇聚节点 '{Name}' 必须指定转换器", Name);
    }
}

/// <summary>
///     汇聚条件枚举
/// </summary>
public enum JoinCondition
{
    /// <summary>
    ///     所有分支都完成
    /// </summary>
    All,

    /// <summary>
    ///     任一分支完成
    /// </summary>
    Any,

    /// <summary>
    ///     自定义脚本条件
    /// </summary>
    Custom
}