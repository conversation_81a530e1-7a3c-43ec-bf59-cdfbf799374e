﻿using Coder.ScriptWorkflow.Nodes;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Coder.ScriptWorkflow.Mapping;

/// <summary>
/// </summary>
internal class ParallelSplitNodeMapping : IEntityTypeConfiguration<ParallelSplitNode>
{
    /// <summary>
    /// </summary>
    /// <param name="builder"></param>
    public void Configure(EntityTypeBuilder<ParallelSplitNode> builder)
    {
        builder.HasBaseType<ParallelNode>();
        builder.Property(_ => _.Name).HasMaxLength(32);

        builder.HasMany(_ => _.NextNodes);

     
        builder.Ignore(_ => _.Auto);
    }
}