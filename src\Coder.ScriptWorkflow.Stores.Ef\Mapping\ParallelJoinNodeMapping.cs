﻿using Coder.ScriptWorkflow.Nodes;
using Innofactor.EfCoreJsonValueConverter;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Coder.ScriptWorkflow.Mapping;

/// <summary>
/// </summary>
internal class ParallelJoinNodeMapping : IEntityTypeConfiguration<ParallelJoinNode>
{
    /// <summary>
    /// </summary>
    /// <param name="builder"></param>
    public void Configure(EntityTypeBuilder<ParallelJoinNode> builder)
    {
        builder.HasBaseType<SingleNode>();
        builder.Property(_ => _.Name).HasMaxLength(32);

        builder.Property(_ => _.JoinCondition).HasMaxLength(50);


        builder.Property(_ => _.CustomJoinScript).HasColumnType("text");

        builder.Property(_ => _.WaitForWorkTasks).HasColumnType("text").HasJsonValueConversion();
        builder.Ignore(_ => _.Auto);
    }
}